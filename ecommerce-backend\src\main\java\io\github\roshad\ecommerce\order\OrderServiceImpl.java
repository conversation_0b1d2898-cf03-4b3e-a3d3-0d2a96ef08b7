package io.github.roshad.ecommerce.order;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

@Service
@RequiredArgsConstructor
public class OrderServiceImpl implements OrderService {
    private final OrderMapper orderMapper;
    private final OrderStatusService orderStatusService;
    private final io.github.roshad.ecommerce.auth.UserService userService;

    @Override
    public Order createOrder(Order order) {
        // 设置默认状态为PENDING
        OrderStatus pendingStatus = orderStatusService.findByStatusName(OrderStatus.PENDING);
        order.setStatus(pendingStatus);
        order.setStatusId(pendingStatus.getId());
        order.setCreatedAt(LocalDateTime.now());
        order.setUpdatedAt(LocalDateTime.now());
        orderMapper.insert(order);
        return order;
    }

    @Override
    public Order getOrderById(Long id) {
        return orderMapper.findById(id);
    }

    @Override
    public List<Order> getOrdersByUserId(Long userId) {
        return orderMapper.findByUserId(userId);
    }

    @Override
    public void updateOrderStatus(Long orderId, String statusName) {
        Order order = orderMapper.findById(orderId);
        if (order != null) {
            OrderStatus newStatus = orderStatusService.findByStatusName(statusName);
            if (newStatus != null) {
                order.setStatus(newStatus);
                order.setStatusId(newStatus.getId());
                order.setUpdatedAt(LocalDateTime.now());
                orderMapper.update(order);
            }
        }
    }

    @Override
    public void cancelOrder(Long orderId) {
        updateOrderStatus(orderId, OrderStatus.CANCELLED);
    }
    @Override
    public boolean isOrderOwner(Long orderId, String username) {
        Order order = orderMapper.findById(orderId);
        if (order == null) {
            return false;
        }
        Long userId = userService.getUserId(username);
        return order.getUserId().equals(userId);
    }

    @Override
    public List<Order> getAllOrders() {
        return orderMapper.findAll();
    }
}