import { create } from 'zustand';
import { persist } from 'zustand/middleware';

// 購物車商品接口
export interface CartItem {
  id: number;
  name: string;
  price: number;
  imageUrl: string;
  quantity: number;
  stock: number;
}

// 購物車狀態接口
interface CartState {
  items: CartItem[];
  totalItems: number;
  totalPrice: number;
  
  // 操作方法
  addItem: (product: Omit<CartItem, 'quantity'>) => void;
  removeItem: (id: number) => void;
  updateQuantity: (id: number, quantity: number) => void;
  clearCart: () => void;
  getItemQuantity: (id: number) => number;
}

// 計算總數量
const calculateTotalItems = (items: CartItem[]): number => {
  return items.reduce((total, item) => total + item.quantity, 0);
};

// 計算總價格
const calculateTotalPrice = (items: CartItem[]): number => {
  return items.reduce((total, item) => total + (item.price * item.quantity), 0);
};

// 創建購物車 store
export const useCartStore = create<CartState>()(
  persist(
    (set, get) => ({
      items: [],
      totalItems: 0,
      totalPrice: 0,

      // 添加商品到購物車
      addItem: (product) => {
        const { items } = get();
        const existingItem = items.find(item => item.id === product.id);

        let newItems: CartItem[];
        
        if (existingItem) {
          // 如果商品已存在，增加數量（不超過庫存）
          const newQuantity = Math.min(existingItem.quantity + 1, product.stock);
          newItems = items.map(item =>
            item.id === product.id
              ? { ...item, quantity: newQuantity }
              : item
          );
        } else {
          // 如果是新商品，添加到購物車
          newItems = [...items, { ...product, quantity: 1 }];
        }

        set({
          items: newItems,
          totalItems: calculateTotalItems(newItems),
          totalPrice: calculateTotalPrice(newItems),
        });
      },

      // 從購物車移除商品
      removeItem: (id) => {
        const { items } = get();
        const newItems = items.filter(item => item.id !== id);
        
        set({
          items: newItems,
          totalItems: calculateTotalItems(newItems),
          totalPrice: calculateTotalPrice(newItems),
        });
      },

      // 更新商品數量
      updateQuantity: (id, quantity) => {
        const { items } = get();
        
        if (quantity <= 0) {
          // 如果數量為0或負數，移除商品
          get().removeItem(id);
          return;
        }

        const newItems = items.map(item => {
          if (item.id === id) {
            // 確保數量不超過庫存
            const newQuantity = Math.min(quantity, item.stock);
            return { ...item, quantity: newQuantity };
          }
          return item;
        });

        set({
          items: newItems,
          totalItems: calculateTotalItems(newItems),
          totalPrice: calculateTotalPrice(newItems),
        });
      },

      // 清空購物車
      clearCart: () => {
        set({
          items: [],
          totalItems: 0,
          totalPrice: 0,
        });
      },

      // 獲取特定商品的數量
      getItemQuantity: (id) => {
        const { items } = get();
        const item = items.find(item => item.id === id);
        return item ? item.quantity : 0;
      },
    }),
    {
      name: 'cart-storage', // 本地存儲的key
      // 可以選擇性地存儲某些字段
      partialize: (state) => ({
        items: state.items,
        totalItems: state.totalItems,
        totalPrice: state.totalPrice,
      }),
    }
  )
);
