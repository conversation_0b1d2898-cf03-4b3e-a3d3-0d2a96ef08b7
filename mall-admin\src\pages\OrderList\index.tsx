import { PageContainer, ProTable, ProColumns } from '@ant-design/pro-components';
import { message, Popconfirm, Button } from 'antd';
import React, { useRef } from 'react';
import API from '@/services/ant-design-pro/api';
import { history } from '@umijs/max';

const OrderList: React.FC = () => {
  const actionRef = useRef<any>();

  const handleRemove = async (id: number) => {
    try {
      await API.Order.cancelOrder({ id });
      message.success('删除成功');
      actionRef.current?.reload();
    } catch (error) {
      message.error('删除失败，请重试');
    }
  };

  const columns: ProColumns<API.Order>[] = [
    {
      title: '订单ID',
      dataIndex: 'id',
      valueType: 'text',
      search: false,
      render: (text, record) => (
        <a onClick={() => history.push(`/order/detail/${record.id}`)}>{text}</a>
      ),
    },
    {
      title: '用户名',
      dataIndex: 'userName',
      valueType: 'text',
      search: false,
    },
    {
      title: '总价',
      dataIndex: 'totalAmount',
      valueType: 'money',
      search: false,
    },
    {
      title: '状态',
      dataIndex: ['status', 'statusName'],
      valueType: 'text',
      search: false,
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      valueType: 'dateTime',
      search: false,
    },
    {
      title: '更新时间',
      dataIndex: 'updatedAt',
      valueType: 'dateTime',
      search: false,
    },
    {
      title: '商品详情',
      dataIndex: 'products',
      valueType: 'text',
      search: false,
      render: (_, record) => (
        <div>
          {record.products?.map((product, index) => (
            <div key={index}>
              {product.name} - {product.price} x {product.quantity}
            </div>
          ))}
        </div>
      ),
    },
    {
      title: '操作',
      dataIndex: 'option',
      valueType: 'option',
      render: (_: any, record: API.Order) => [
        <Popconfirm
          key="delete"
          title="确定删除该订单吗？"
          onConfirm={() => handleRemove(record.id!)}
          okText="是"
          cancelText="否"
        >
          <Button type="link" danger>
            删除
          </Button>
        </Popconfirm>,
      ],
    },
  ];



  return (
    <PageContainer>
      <ProTable<API.Order>
        headerTitle="订单列表"
        actionRef={actionRef}
        rowKey="id"
        request={async (params) => {
          const allOrders = await API.Order.getAllOrders(); // 获取所有订单数据

          const { current, pageSize } = params;
          const startIndex = (current! - 1) * pageSize!;
          const endIndex = startIndex + pageSize!;
          const paginatedData = allOrders.slice(startIndex, endIndex);

          return {
            data: paginatedData,
            success: true,
            total: allOrders.length,
          };
        }}
        columns={columns}
        pagination={{
          defaultPageSize: 10, // 设置默认分页大小
          showSizeChanger: true,
        }}
        toolBarRender={() => []}
      />
    </PageContainer>
  );
};

export default OrderList;
