'use client';

import React, { useState, useEffect } from 'react';
import { useSearchParams } from 'next/navigation';
import ProductCard from '../../components/Product/ProductCard';
import Link from 'next/link';

interface Product {
  id: number;
  name: string;
  description: string;
  price: number;
  stock: number;
  imageUrl: string;
  categoryId?: number;
}

export default function SearchPage() {
  const searchParams = useSearchParams();
  const query = searchParams.get('q') || '';
  
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchSearchResults = async () => {
      if (!query.trim()) {
        setProducts([]);
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        // 實際項目中會調用搜索API
        // const response = await fetch(`http://localhost:8080/api/products/search?q=${encodeURIComponent(query)}`);
        
        // 模擬搜索：先獲取所有商品，然後在前端篩選
        const response = await fetch("http://localhost:8080/api/products");
        if (!response.ok) {
          throw new Error("搜索失敗");
        }
        
        const allProducts = await response.json();
        
        // 簡單的搜索邏輯：匹配商品名稱或描述
        const searchResults = allProducts.filter((product: Product) =>
          product.name.toLowerCase().includes(query.toLowerCase()) ||
          product.description.toLowerCase().includes(query.toLowerCase())
        );
        
        setProducts(searchResults);
      } catch (err: unknown) {
        if (err instanceof Error) {
          setError(err.message);
        } else {
          setError(String(err));
        }
      } finally {
        setLoading(false);
      }
    };

    fetchSearchResults();
  }, [query]);

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center py-8">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto"></div>
          <p className="mt-4 text-gray-600">搜索中...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-red-500 text-center py-8">
          <p>搜索出錯: {error}</p>
          <Link 
            href="/" 
            className="mt-4 inline-block px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
          >
            返回首頁
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {/* 搜索結果標題 */}
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-900 mb-2">
          搜索結果
        </h1>
        <p className="text-gray-600">
          關鍵詞: "<span className="font-semibold">{query}</span>"
        </p>
        <p className="text-sm text-gray-500 mt-1">
          找到 {products.length} 件商品
        </p>
      </div>

      {/* 搜索結果 */}
      {products.length === 0 ? (
        <div className="text-center py-12">
          <div className="text-gray-400 mb-4">
            <svg className="mx-auto h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">沒有找到相關商品</h3>
          <p className="text-gray-500 mb-6">
            嘗試使用不同的關鍵詞或瀏覽我們的商品分類
          </p>
          <div className="space-x-4">
            <Link 
              href="/" 
              className="inline-block px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
            >
              瀏覽所有商品
            </Link>
            <button 
              onClick={() => window.history.back()}
              className="inline-block px-4 py-2 border border-gray-300 text-gray-700 rounded hover:bg-gray-50"
            >
              返回上一頁
            </button>
          </div>
        </div>
      ) : (
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
          {products.map((product) => (
            <ProductCard key={product.id} product={product} />
          ))}
        </div>
      )}

      {/* 返回首頁鏈接 */}
      <div className="mt-8 text-center">
        <Link 
          href="/" 
          className="inline-flex items-center px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
        >
          ← 返回首頁
        </Link>
      </div>
    </div>
  );
}
