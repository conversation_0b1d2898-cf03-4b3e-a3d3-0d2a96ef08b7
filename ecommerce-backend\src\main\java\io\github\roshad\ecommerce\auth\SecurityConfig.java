package io.github.roshad.ecommerce.auth;

import io.github.roshad.ecommerce.order.OrderService;
import io.github.roshad.ecommerce.auth.UserService;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpMethod;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.core.Authentication;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;
import org.springframework.security.authorization.AuthorizationDecision;
import org.springframework.security.authorization.AuthorizationManager;
import org.springframework.security.web.access.intercept.RequestAuthorizationContext;
import org.springframework.web.cors.CorsConfigurationSource;

import java.util.function.Supplier;

@Configuration
@EnableWebSecurity
public class SecurityConfig {

    private final JwtAuthenticationFilter jwtAuthenticationFilter;
    private final OrderService orderService;
    private final UserService userService;
    private final CorsConfigurationSource corsConfigurationSource;

    @Bean
    public SecurityFilterChain securityFilterChain(HttpSecurity http) throws Exception {
        http
            .csrf(csrf -> csrf.disable())
            .cors(cors -> cors.configurationSource(corsConfigurationSource))
            .authorizeHttpRequests(authorize -> authorize
                .requestMatchers("/api/auth/login", "/api/auth/register").permitAll()
                .requestMatchers("/swagger-ui.html", "/swagger-ui/**", "/v3/api-docs/**", "/webjars/**", "/swagger-resources/**").permitAll()
                .requestMatchers("/api/products/**").permitAll() // 允许所有对 /api/products 路径的访问
                .requestMatchers(HttpMethod.POST, "/api/orders").hasRole("USER")
                .requestMatchers(HttpMethod.GET, "/api/orders/{id}").access(orderOwnershipAuthorizationManager())
                .requestMatchers(HttpMethod.GET, "/api/orders/user/{userId}").access(sameUserAuthorizationManager())
                .requestMatchers(HttpMethod.PATCH, "/api/orders/**/status").hasRole("ADMIN")
                .requestMatchers(HttpMethod.DELETE, "/api/orders/{id}").access(orderOwnershipAuthorizationManager())
                .anyRequest().authenticated()
            )
            .addFilterBefore(jwtAuthenticationFilter, UsernamePasswordAuthenticationFilter.class);
        return http.build();
    }

    public SecurityConfig(JwtAuthenticationFilter jwtAuthenticationFilter,
                          OrderService orderService,
                          UserService userService,
                          CorsConfigurationSource corsConfigurationSource) {
        this.jwtAuthenticationFilter = jwtAuthenticationFilter;
        this.orderService = orderService;
        this.userService = userService;
        this.corsConfigurationSource = corsConfigurationSource;
    }

    @Bean
    public AuthorizationManager<RequestAuthorizationContext> orderOwnershipAuthorizationManager() {
        return (authentication, context) -> {
            String orderId = context.getVariables().get("id");
            if (orderId == null) {
                return new AuthorizationDecision(false);
            }
            
            String username = authentication.get().getName();
            boolean isOwner = orderService.isOrderOwner(Long.parseLong(orderId), username);
            return new AuthorizationDecision(isOwner);
        };
    }

    @Bean
    public AuthorizationManager<RequestAuthorizationContext> sameUserAuthorizationManager() {
        return (authentication, context) -> {
            String userId = context.getVariables().get("userId");
            if (userId == null) {
                return new AuthorizationDecision(false);
            }
            
            String username = authentication.get().getName();
            Long userIdFromToken = userService.getUserId(username);
            boolean isSameUser = userIdFromToken != null && userIdFromToken.equals(Long.parseLong(userId));
            return new AuthorizationDecision(isSameUser);
        };
    }
}