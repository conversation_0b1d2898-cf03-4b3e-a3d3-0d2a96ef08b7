package io.github.roshad.ecommerce.order; // Test for hot r1

import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/orders")
@RequiredArgsConstructor
public class OrderController {
    private final OrderService orderService;

    @PostMapping
    public ResponseEntity<Order> createOrder(@RequestBody Order order, 
                                            @RequestHeader("Authorization") String token) {
        // 从token解析用户ID并验证
        Order createdOrder = orderService.createOrder(order);
        return ResponseEntity.status(HttpStatus.CREATED).body(createdOrder);
    }

    @GetMapping("/{id}")
    public ResponseEntity<Order> getOrderById(@PathVariable Long id, 
                                             @RequestHeader("Authorization") String token) {
        // 验证用户是否有权访问该订单
        Order order = orderService.getOrderById(id);
        return ResponseEntity.ok(order);
    }

    @GetMapping("/user/{userId}")
    public ResponseEntity<List<Order>> getOrdersByUserId(@PathVariable Long userId, 
                                                        @RequestHeader("Authorization") String token) {
        // 验证token中的用户ID与路径参数一致
        List<Order> orders = orderService.getOrdersByUserId(userId);
        return ResponseEntity.ok(orders);
    }
    
        @GetMapping
        @PreAuthorize("hasRole('ADMIN')")
        public ResponseEntity<List<Order>> getAllOrders() {
            List<Order> orders = orderService.getAllOrders();
            return ResponseEntity.ok(orders);
        }

    @PatchMapping("/{id}/status")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<Void> updateOrderStatus(@PathVariable Long id, 
                                                 @RequestBody OrderStatusUpdateRequest request) {
        orderService.updateOrderStatus(id, request.getStatusName());
        return ResponseEntity.noContent().build();
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<Void> cancelOrder(@PathVariable Long id, 
                                           @RequestHeader("Authorization") String token) {
        // 验证用户是否有权取消该订单
        orderService.cancelOrder(id);
        return ResponseEntity.noContent().build();
    }
}