'use client';

import React from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { TrashIcon, MinusIcon, PlusIcon } from '@heroicons/react/24/outline';
import { CartItem as CartItemType, useCartStore } from '../../store/cartStore';

interface CartItemProps {
  item: CartItemType;
}

const CartItem: React.FC<CartItemProps> = ({ item }) => {
  const { updateQuantity, removeItem } = useCartStore();

  // 處理圖片URL
  const getImageUrl = (url: string) => {
    if (!url) return '/placeholder.jpg';
    
    if (url.startsWith('/images/')) {
      return `https://via.placeholder.com/150x150/f0f0f0/666666?text=${encodeURIComponent(item.name)}`;
    }
    
    try {
      new URL(url);
      return url;
    } catch {
      return `https://via.placeholder.com/150x150/f0f0f0/666666?text=${encodeURIComponent(item.name)}`;
    }
  };

  const handleQuantityChange = (newQuantity: number) => {
    if (newQuantity > item.stock) {
      // 如果超過庫存，顯示提示
      alert(`庫存不足，最多只能添加 ${item.stock} 件`);
      return;
    }
    updateQuantity(item.id, newQuantity);
  };

  const handleRemove = () => {
    if (confirm(`確定要從購物車中移除 "${item.name}" 嗎？`)) {
      removeItem(item.id);
    }
  };

  return (
    <div className="flex items-center space-x-4 p-4 bg-white border border-gray-200 rounded-lg shadow-sm">
      {/* 商品圖片 */}
      <div className="flex-shrink-0">
        <Link href={`/products/${item.id}`}>
          <div className="relative w-20 h-20 cursor-pointer">
            <Image
              src={getImageUrl(item.imageUrl)}
              alt={item.name}
              fill
              style={{ objectFit: "cover" }}
              className="rounded-md"
            />
          </div>
        </Link>
      </div>

      {/* 商品信息 */}
      <div className="flex-grow min-w-0">
        <Link href={`/products/${item.id}`}>
          <h3 className="text-lg font-medium text-gray-900 hover:text-blue-600 cursor-pointer line-clamp-2">
            {item.name}
          </h3>
        </Link>
        <p className="text-sm text-gray-500 mt-1">
          庫存: {item.stock} 件
        </p>
        <p className="text-lg font-semibold text-green-600 mt-1">
          ¥{item.price.toFixed(2)}
        </p>
      </div>

      {/* 數量控制 */}
      <div className="flex items-center space-x-2">
        <button
          onClick={() => handleQuantityChange(item.quantity - 1)}
          disabled={item.quantity <= 1}
          className={`p-1 rounded-md ${
            item.quantity <= 1
              ? 'text-gray-300 cursor-not-allowed'
              : 'text-gray-600 hover:text-gray-800 hover:bg-gray-100'
          }`}
        >
          <MinusIcon className="w-4 h-4" />
        </button>
        
        <span className="w-12 text-center font-medium">
          {item.quantity}
        </span>
        
        <button
          onClick={() => handleQuantityChange(item.quantity + 1)}
          disabled={item.quantity >= item.stock}
          className={`p-1 rounded-md ${
            item.quantity >= item.stock
              ? 'text-gray-300 cursor-not-allowed'
              : 'text-gray-600 hover:text-gray-800 hover:bg-gray-100'
          }`}
        >
          <PlusIcon className="w-4 h-4" />
        </button>
      </div>

      {/* 小計 */}
      <div className="text-right min-w-0">
        <p className="text-lg font-semibold text-gray-900">
          ¥{(item.price * item.quantity).toFixed(2)}
        </p>
      </div>

      {/* 刪除按鈕 */}
      <div className="flex-shrink-0">
        <button
          onClick={handleRemove}
          className="p-2 text-red-500 hover:text-red-700 hover:bg-red-50 rounded-md transition-colors"
          title="移除商品"
        >
          <TrashIcon className="w-5 h-5" />
        </button>
      </div>
    </div>
  );
};

export default CartItem;
