$OutputEncoding = [System.Text.Encoding]::UTF8
# 创建Next.js项目
npx create-next-app mall-client --typescript --eslint

# 进入项目目录
cd mall-client

# 安装依赖
npm install tailwindcss postcss autoprefixer @heroicons/react zustand @tanstack/react-query

# 初始化Tailwind配置
npx tailwindcss init -p

# 配置tailwind.config.js - 替换文件内容
$tailwindConfigContent = @"
/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    './pages/**/*.{js,ts,jsx,tsx,mdx}',
    './components/**/*.{js,ts,jsx,tsx,mdx}',
    './app/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  theme: {
    extend: {
      backgroundImage: {
        'gradient-radial': 'radial-gradient(var(--tw-gradient-stops))',
        'gradient-conic':
          'conic-gradient(from 180deg at 50% 50%, var(--tw-gradient-stops))',
      },
    },
  },
  plugins: [],
}
"@
Set-Content -Path "tailwind.config.js" -Value $tailwindConfigContent

# 修改globals.css，添加Tailwind指令
$globalsCssContent = @"
@tailwind base;
@tailwind components;
@tailwind utilities;
"@
Set-Content -Path "src/app/globals.css" -Value $globalsCssContent

# 创建 README.md
$readmeContent = @"
# mall-client

这是一个基于 Next.js 和 Tailwind CSS 构建的电商用户购物端。
"@
Set-Content -Path "README.md" -Value $readmeContent

Write-Host "mall-client 项目初始化完成！"