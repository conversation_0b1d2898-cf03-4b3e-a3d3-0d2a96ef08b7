'use client';

import React from 'react';
import { useCartStore } from '../../store/cartStore';

interface CartSummaryProps {
  onCheckout?: () => void;
}

const CartSummary: React.FC<CartSummaryProps> = ({ onCheckout }) => {
  const { totalItems, totalPrice, items } = useCartStore();

  // 計算運費（模擬邏輯）
  const shippingFee = totalPrice >= 99 ? 0 : 10;
  const finalTotal = totalPrice + shippingFee;

  // 計算節省金額（免運費）
  const savedAmount = totalPrice >= 99 ? 10 : 0;

  return (
    <div className="bg-white border border-gray-200 rounded-lg shadow-sm p-6">
      <h2 className="text-xl font-semibold text-gray-900 mb-4">訂單摘要</h2>
      
      {/* 商品統計 */}
      <div className="space-y-3 mb-4">
        <div className="flex justify-between text-gray-600">
          <span>商品數量</span>
          <span>{totalItems} 件</span>
        </div>
        
        <div className="flex justify-between text-gray-600">
          <span>商品小計</span>
          <span>¥{totalPrice.toFixed(2)}</span>
        </div>
        
        <div className="flex justify-between text-gray-600">
          <span>運費</span>
          <span className={shippingFee === 0 ? 'text-green-600' : ''}>
            {shippingFee === 0 ? '免費' : `¥${shippingFee.toFixed(2)}`}
          </span>
        </div>
        
        {savedAmount > 0 && (
          <div className="flex justify-between text-green-600 text-sm">
            <span>已節省運費</span>
            <span>-¥{savedAmount.toFixed(2)}</span>
          </div>
        )}
      </div>

      {/* 免運費提示 */}
      {totalPrice < 99 && totalPrice > 0 && (
        <div className="bg-blue-50 border border-blue-200 rounded-md p-3 mb-4">
          <p className="text-sm text-blue-700">
            再購買 ¥{(99 - totalPrice).toFixed(2)} 即可享受免運費！
          </p>
        </div>
      )}

      {/* 分隔線 */}
      <div className="border-t border-gray-200 pt-4 mb-4">
        <div className="flex justify-between items-center">
          <span className="text-lg font-semibold text-gray-900">總計</span>
          <span className="text-2xl font-bold text-green-600">
            ¥{finalTotal.toFixed(2)}
          </span>
        </div>
      </div>

      {/* 結算按鈕 */}
      <button
        onClick={onCheckout}
        disabled={items.length === 0}
        className={`w-full py-3 px-4 rounded-lg font-semibold transition-colors ${
          items.length === 0
            ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
            : 'bg-blue-600 text-white hover:bg-blue-700'
        }`}
      >
        {items.length === 0 ? '購物車為空' : '前往結算'}
      </button>

      {/* 繼續購物按鈕 */}
      <button
        onClick={() => window.history.back()}
        className="w-full mt-3 py-2 px-4 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
      >
        繼續購物
      </button>

      {/* 安全提示 */}
      <div className="mt-4 text-center">
        <div className="flex items-center justify-center space-x-2 text-sm text-gray-500">
          <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clipRule="evenodd" />
          </svg>
          <span>安全結算</span>
        </div>
        <p className="text-xs text-gray-400 mt-1">
          您的支付信息將被安全加密
        </p>
      </div>
    </div>
  );
};

export default CartSummary;
